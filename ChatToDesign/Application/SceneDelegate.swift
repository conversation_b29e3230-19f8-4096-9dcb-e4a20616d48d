//
//  SceneDelegate.swift
//  ChatToDesign
//
//  Created by AI Assistant on 2025/7/21.
//

import AppsFlyerLib
import GoogleSignIn
import SwiftUI
import UIKit

class SceneDelegate: NSObject, UIWindowSceneDelegate {

  var window: UIWindow?

  // MARK: - Scene Lifecycle

  func scene(
    _ scene: UIScene, willConnectTo session: UISceneSession,
    options connectionOptions: UIScene.ConnectionOptions
  ) {
    Logger.info("SceneDelegate: Scene will connect")

    // 处理启动时的 Universal Links
    if let userActivity = connectionOptions.userActivities.first {
      handleUniversalLink(userActivity)
    }

    // 处理启动时的 URL Schemes
    if let urlContext = connectionOptions.urlContexts.first {
      handleURLScheme(urlContext.url)
    }
  }

  func sceneDidBecomeActive(_ scene: UIScene) {
    Logger.info("SceneDelegate: Scene did become active")

    // 通知 AppDelegate 场景变为活跃状态
    if let appDelegate = UIApplication.shared.delegate as? AppDelegate {
      appDelegate.applicationDidBecomeActive(UIApplication.shared)
    }
  }

  func sceneWillResignActive(_ scene: UIScene) {
    Logger.info("SceneDelegate: Scene will resign active")
  }

  func sceneWillEnterForeground(_ scene: UIScene) {
    Logger.info("SceneDelegate: Scene will enter foreground")
  }

  func sceneDidEnterBackground(_ scene: UIScene) {
    Logger.info("SceneDelegate: Scene did enter background")
  }

  // MARK: - Universal Links Handling

  func scene(_ scene: UIScene, continue userActivity: NSUserActivity) {
    Logger.info("SceneDelegate: Received Universal Link")
    handleUniversalLink(userActivity)
  }

  private func handleUniversalLink(_ userActivity: NSUserActivity) {
    guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
      let url = userActivity.webpageURL
    else {
      Logger.warning("SceneDelegate: Invalid user activity for Universal Link")
      return
    }

    Logger.info("SceneDelegate: Processing Universal Link - \(url.absoluteString)")

    // 1. 通知 AppsFlyer SDK
    AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)

    // 2. 处理深度链接路由
    let container = AppDependencyContainer.shared
    container.deepLinkHandler.processDeepLink(from: url)

    Logger.info("SceneDelegate: Universal Link processed successfully")
  }

  // MARK: - URL Schemes Handling

  func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
    Logger.info("SceneDelegate: Received URL Scheme")

    guard let urlContext = URLContexts.first else {
      Logger.warning("SceneDelegate: No URL context found")
      return
    }

    handleURLScheme(urlContext.url)
  }

  private func handleURLScheme(_ url: URL) {
    Logger.info("SceneDelegate: Processing URL Scheme - \(url.absoluteString)")

    // 1. 检查是否是 Google Sign-In URL
    if GIDSignIn.sharedInstance.handle(url) {
      Logger.info("SceneDelegate: Google Sign-In URL handled")
      return
    }

    // 2. 检查是否是 AppsFlyer 深度链接
    let container = AppDependencyContainer.shared
    container.deepLinkHandler.processDeepLink(from: url)

    Logger.info("SceneDelegate: URL Scheme processed successfully")
  }

  // MARK: - Error Handling

  private func handleDeepLinkError(_ error: Error) {
    Logger.error("SceneDelegate: Deep link processing failed - \(error.localizedDescription)")

    // 可以在这里添加错误处理逻辑，比如显示错误提示或跳转到默认页面
    // 例如：跳转到主页
    DispatchQueue.main.async {
      NotificationCenter.default.post(
        name: .deepLinkRouteToDefault,
        object: nil,
        userInfo: ["error": error.localizedDescription]
      )
    }
  }
}
