//
//  ChatToDesignApp.swift
//  ChatToDesign
//
//  Created by <PERSON><PERSON><PERSON> on 2025/3/14.
//

import SwiftUI

extension Notification.Name {
  static let showSubscriptionPage = Notification.Name("showSubscriptionPage")
  static let navigateToProfile = Notification.Name("navigateToProfile")
}

@main
struct ChatToDesignApp: App {
  // Register AppDelegate
  @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

  // Environment variables
  @Environment(\.scenePhase) private var scenePhase

  var body: some Scene {
    WindowGroup {
      ZStack {
        RootView()
      }
      .preferredColorScheme(.dark)
      .onOpenURL { url in
        handleURL(url)
      }
      .onContinueUserActivity(NSUserActivityTypeBrowsingWeb) { userActivity in
        handleUniversalLink(userActivity)
      }
    }
    .onChange(of: scenePhase) { newPhase in
      switch newPhase {
      case .active:
        Logger.info("App: Scene became active")
      case .inactive:
        Logger.info("App: Scene became inactive")
      case .background:
        Logger.info("App: Scene moved to background")
      @unknown default:
        Logger.info("App: Scene changed to unknown state")
      }
    }
  }

  // MARK: - URL Handling

  private func handleURL(_ url: URL) {
    Logger.info("App: Processing URL Scheme - \(url.absoluteString)")

    // Handle Google Sign-In URL
    if GIDSignIn.sharedInstance.handle(url) {
      Logger.info("App: Google Sign-In URL handled")
      return
    }

    // Handle AppsFlyer deep links
    let container = AppDependencyContainer.shared
    container.deepLinkHandler.processDeepLink(from: url)

    Logger.info("App: URL Scheme processed successfully")
  }

  private func handleUniversalLink(_ userActivity: NSUserActivity) {
    guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
      let url = userActivity.webpageURL
    else {
      Logger.warning("App: Invalid user activity for Universal Link")
      return
    }

    Logger.info("App: Processing Universal Link - \(url.absoluteString)")

    // Notify AppsFlyer SDK
    AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)

    // Handle deep link routing
    let container = AppDependencyContainer.shared
    container.deepLinkHandler.processDeepLink(from: url)

    Logger.info("App: Universal Link processed successfully")
  }
}
