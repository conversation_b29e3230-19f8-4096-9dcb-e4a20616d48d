# AppsFlyer OneLink Deep Linking Integration Plan

## 项目概述

本文档详细说明如何在 ChatToDesign iOS 应用中集成 AppsFlyer OneLink 深度链接功能，使用 iOS Universal Links 作为主要实现方式。

## 当前架构分析

### 现有 AppsFlyer 集成状态

- ✅ AppsFlyer SDK 已集成 (`AppsFlyerLib`)
- ✅ 基础配置已完成 (`AppsFlyerAnalyticsAdapter`)
- ✅ 深度链接处理器已实现 (`AppsFlyerDeepLinkHandler`)
- ✅ 依赖注入容器已配置 (`AppDependencyContainer`)
- ✅ Universal Links 域名已配置 (`picadabra-ai.onelink.me`)

### 现有 URL 处理

- Google Sign-In URL Scheme 已配置
- AppDelegate 中已有基础 URL 处理逻辑
- 缺少 Universal Links 的 `continueUserActivity` 处理

## 技术方案

### 1. Universal Links 完整配置

#### 1.1 Apple-App-Site-Association (AASA) 文件

AppsFlyer 会自动为你的 OneLink 域名生成 AASA 文件，位于：

```
https://picadabra-ai.onelink.me/.well-known/apple-app-site-association
```

AASA 文件内容示例：

```json
{
  "applinks": {
    "apps": [],
    "details": [
      {
        "appID": "TEAM_ID.com.a1d.chat-to-design",
        "paths": ["*"]
      }
    ]
  }
}
```

#### 1.2 Entitlements 配置

当前已配置：

```xml
<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:picadabra-ai.onelink.me</string>
</array>
```

#### 1.3 iOS 13+ Scene-Based 实现

✅ **已实现**: 使用专门的 SceneDelegate 来处理 Universal Links，这是 iOS 13+ 的最佳实践。

##### 1.3.1 SceneDelegate 实现

已创建 `ChatToDesign/Application/SceneDelegate.swift`，包含：

- Universal Links 处理 (`scene(_:continue:)`)
- URL Schemes 处理 (`scene(_:openURLContexts:)`)
- 场景生命周期管理
- 错误处理和日志记录

##### 1.3.2 ChatToDesignApp.swift 更新

已更新为使用 SceneDelegate：

```swift
@main
struct ChatToDesignApp: App {
    // 注册 AppDelegate
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    // 注册 SceneDelegate
    @UIApplicationDelegateAdaptor(SceneDelegate.self) var sceneDelegate

    // 环境变量
    @Environment(\.scenePhase) private var scenePhase

    var body: some Scene {
        WindowGroup {
            ZStack {
                RootView()
            }
            .preferredColorScheme(.dark)
        }
        .onChange(of: scenePhase) { newPhase in
            // 处理场景状态变化
        }
    }
}
```

##### 1.3.3 Info.plist 配置

已添加 Scene 配置：

```xml
<key>UIApplicationSceneManifest</key>
<dict>
    <key>UIApplicationSupportsMultipleScenes</key>
    <false/>
    <key>UISceneConfigurations</key>
    <dict>
        <key>UIWindowSceneSessionRoleApplication</key>
        <array>
            <dict>
                <key>UISceneConfigurationName</key>
                <string>Default Configuration</string>
                <key>UISceneDelegateClassName</key>
                <string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
            </dict>
        </array>
    </dict>
</dict>
```

### 2. AppsFlyer UDL (Unified Deep Linking) 集成

#### 2.1 配置 Deep Link Delegate

在 `AppsFlyerAnalyticsAdapter` 中已实现，需要确保：

```swift
// 在配置时设置 delegate
AppsFlyerLib.shared().deepLinkDelegate = self
```

#### 2.2 实现 AppsFlyerDeepLinkDelegate

```swift
extension AppsFlyerAnalyticsAdapter: AppsFlyerDeepLinkDelegate {
    func didResolveDeepLink(_ result: AppsFlyerDeepLinkResult) {
        // 委托给现有的 DeepLinkHandler 处理
        let container = AppDependencyContainer.shared
        container.deepLinkHandler.processDeepLink(result)
    }
}
```

### 3. OneLink 链接结构设计

#### 3.1 基础链接格式

```
https://picadabra-ai.onelink.me/{template_id}?pid={media_source}&deep_link_value={destination}&deep_link_sub1={parameter1}
```

#### 3.2 深度链接值映射

| deep_link_value | 目标页面        | 描述         |
| --------------- | --------------- | ------------ |
| `create`        | CreatePageView  | 创建页面     |
| `explore`       | ExplorePageView | 探索页面     |
| `profile`       | ProfilePageView | 个人资料页面 |
| `subscription`  | PaywallView     | 订阅页面     |
| `chat`          | 聊天功能        | 特定聊天会话 |
| `template`      | 模板详情        | 特定模板页面 |

#### 3.3 参数设计

- `deep_link_sub1`: 具体 ID（如 template_id, chat_id）
- `deep_link_sub2`: 额外参数（如 campaign_id）
- `deep_link_sub3`: 用户标识或推荐码
- `campaign`: 营销活动名称
- `media_source`: 流量来源

### 4. 路由系统增强

#### 4.1 现有路由通知扩展

```swift
extension Notification.Name {
    // 现有通知
    static let deepLinkRouteToChat = Notification.Name("deepLinkRouteToChat")
    static let deepLinkRouteToSubscription = Notification.Name("deepLinkRouteToSubscription")

    // 新增通知
    static let deepLinkRouteToTemplate = Notification.Name("deepLinkRouteToTemplate")
    static let deepLinkRouteToSpecificChat = Notification.Name("deepLinkRouteToSpecificChat")
}
```

#### 4.2 MainTabView 路由处理

在 `MainTabView` 中监听深度链接通知：

```swift
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToCreate)) { _ in
    selectedTab = .create
}
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToExplore)) { _ in
    selectedTab = .explore
}
```

### 5. 实现步骤

#### 阶段 1: 基础 Universal Links 支持

1. 更新 AppDelegate 添加 `continueUserActivity` 方法
2. 确保 AppsFlyer Deep Link Delegate 正确配置
3. 测试基础 Universal Links 功能

#### 阶段 2: UDL 集成

1. 实现 `AppsFlyerDeepLinkDelegate` 协议
2. 增强 `AppsFlyerDeepLinkHandler` 路由逻辑
3. 添加新的路由通知和处理

#### 阶段 3: UI 路由集成

1. 在 MainTabView 中添加深度链接监听
2. 实现具体页面的参数处理
3. 添加错误处理和回退逻辑

#### 阶段 4: 测试和优化

1. 创建测试 OneLink 链接
2. 测试各种场景（新用户、现有用户、应用未安装）
3. 性能优化和错误处理完善

### 6. 测试策略

#### 6.1 测试场景

- **新用户安装**: 点击链接 → App Store → 安装 → 首次启动 → 延迟深度链接
- **现有用户**: 点击链接 → 直接打开应用 → 即时深度链接
- **应用在后台**: 点击链接 → 应用前台 → 深度链接处理

#### 6.2 测试链接示例

```
# 跳转到创建页面
https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=create

# 跳转到特定模板
https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=template&deep_link_sub1=video_template_001

# 跳转到订阅页面（带营销参数）
https://picadabra-ai.onelink.me/H5hv?pid=email&c=summer_sale&deep_link_value=subscription&deep_link_sub1=premium
```

### 7. 监控和分析

#### 7.1 关键指标

- 深度链接点击率
- 安装转化率
- 深度链接成功率
- 用户路径分析

#### 7.2 错误监控

- 深度链接解析失败
- 路由目标不存在
- 网络连接问题
- AASA 文件访问失败

## 技术风险和缓解策略

### 风险 1: Universal Links 不工作

**缓解**: 实现 URL Scheme 作为备用方案

### 风险 2: AASA 文件缓存问题

**缓解**: 提供手动刷新机制和错误提示

### 风险 3: 深度链接参数丢失

**缓解**: 实现本地缓存和重试机制

### 风险 4: 用户体验中断

**缓解**: 优雅的错误处理和默认页面跳转

## 下一步行动

1. **立即执行**: 更新 AppDelegate 支持 Universal Links
2. **本周完成**: 实现完整的 UDL 集成
3. **下周测试**: 创建测试链接并验证功能
4. **持续优化**: 根据用户反馈和数据分析持续改进

## 详细实现代码

### AppDelegate 更新

```swift
// 添加到现有的 AppDelegate.swift
func application(
    _ application: UIApplication,
    continue userActivity: NSUserActivity,
    restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void
) -> Bool {
    // 处理 AppsFlyer Universal Links
    AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)

    Logger.info("Universal Link received: \(userActivity.webpageURL?.absoluteString ?? "unknown")")
    return true
}
```

### AppsFlyerAnalyticsAdapter 增强

```swift
// 在 configureAppsFlyerSDK 方法中确保设置 delegate
private func configureAppsFlyerSDK(with config: AppsFlyerConfiguration) async {
    // ... 现有配置代码 ...

    // 确保设置深度链接代理
    if config.deepLinkingEnabled {
        AppsFlyerLib.shared().deepLinkDelegate = self
    }
}

// 实现 AppsFlyerDeepLinkDelegate
extension AppsFlyerAnalyticsAdapter: AppsFlyerDeepLinkDelegate {
    public func didResolveDeepLink(_ result: AppsFlyerDeepLinkResult) {
        logger.info("AppsFlyer UDL: Deep link resolved with status: \(result.status)")

        // 委托给专门的深度链接处理器
        let container = AppDependencyContainer.shared
        container.deepLinkHandler.processDeepLink(result)
    }
}
```

### AppsFlyerDeepLinkHandler 增强

```swift
// 添加 UDL 结果处理方法
public func processDeepLink(_ result: AppsFlyerDeepLinkResult) {
    isProcessingDeepLink = true

    switch result.status {
    case .found:
        handleUDLDeepLinkFound(result)
    case .notFound:
        handleDeepLinkNotFound()
    case .failure:
        if let error = result.error {
            handleDeepLinkFailure(error)
        }
    @unknown default:
        handleUnknownDeepLinkStatus()
    }

    isProcessingDeepLink = false
}

private func handleUDLDeepLinkFound(_ result: AppsFlyerDeepLinkResult) {
    guard let deepLink = result.deepLink else {
        logger.warning("UDL: Deep link found but no data available")
        return
    }

    logger.info("UDL: Processing deep link - isDeferred: \(deepLink.isDeferred)")

    // 提取参数
    let clickEvent = deepLink.clickEvent
    deepLinkData = clickEvent

    // 创建参数对象
    let parameters = extractDeepLinkParameters(from: clickEvent)

    // 如果有 deeplinkValue，优先使用
    if let deeplinkValue = deepLink.deeplinkValue {
        parameters.deepLinkValue = deeplinkValue
    }

    // 路由到目标
    routeToDestination(parameters: parameters)

    // 追踪事件
    trackDeepLinkEvent(parameters: parameters.allParameters, status: "udl_found")
}
```

### MainTabView 路由监听

```swift
// 在 MainTabView 的 body 中添加更多深度链接监听
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToCreate)) { _ in
    selectedTab = .create
}
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToExplore)) { _ in
    selectedTab = .explore
}
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToSubscription)) { _ in
    // 显示订阅页面的逻辑
    NotificationCenter.default.post(name: .showSubscriptionPage, object: nil)
}
```

## 配置检查清单

### ✅ 已完成项目

- [x] AppsFlyer SDK 集成
- [x] 基础配置和依赖注入
- [x] Universal Links 域名配置 (`picadabra-ai.onelink.me`)
- [x] 深度链接处理器基础实现
- [x] 路由通知系统
- [x] SceneDelegate 创建和配置
- [x] ChatToDesignApp.swift 更新为使用 SceneDelegate
- [x] Info.plist Scene 配置
- [x] URL Scheme 配置

### 🔄 需要完成项目

- [ ] AppsFlyerAnalyticsAdapter 实现 `AppsFlyerDeepLinkDelegate`
- [ ] AppsFlyerDeepLinkHandler 添加 UDL 支持
- [ ] MainTabView 添加深度链接路由监听
- [ ] 创建和测试 OneLink 模板

### 🧪 测试计划

- [ ] 验证 AASA 文件可访问性

  ```bash
  # 使用 curl 验证 AASA 文件
  curl -v https://picadabra-ai.onelink.me/.well-known/apple-app-site-association
  ```

- [ ] 测试 Universal Links 基础功能

  ```
  # 在 Safari 中打开测试链接
  https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=create
  ```

- [ ] 测试延迟深度链接（新用户安装）

  1. 卸载应用
  2. 点击深度链接
  3. 安装应用
  4. 验证首次启动时是否路由到正确页面

- [ ] 测试即时深度链接（现有用户）

  1. 确保应用已安装
  2. 点击深度链接
  3. 验证是否直接路由到正确页面

- [ ] 测试各种参数组合

  ```
  # 基础路由测试
  https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=create

  # 带参数的路由测试
  https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=template&deep_link_sub1=template_id_123

  # 营销参数测试
  https://picadabra-ai.onelink.me/H5hv?pid=email&c=promo&deep_link_value=subscription
  ```

## 相关文档

- [AppsFlyer iOS UDL 官方文档](https://dev.appsflyer.com/hc/docs/dl_ios_unified_deep_linking)
- [Apple Universal Links 文档](https://developer.apple.com/documentation/xcode/supporting-universal-links-in-your-app)
- [项目现有 AppsFlyer 集成文档](./AppsFlyer-Integration-Plan.md)
