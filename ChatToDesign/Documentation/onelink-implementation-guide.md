# OneLink 实施指南 (SceneDelegate 版本)

> **⚠️ 已弃用：本文档使用 SceneDelegate 方法**
>
> 此方法已不再推荐用于 SwiftUI 应用。请参考 [SwiftUI URL Handling Best Practices](./swiftui-url-handling-best-practices.md) 了解当前推荐的实现方式。

## 快速开始

这是一个简化的实施指南，帮助你快速集成 AppsFlyer OneLink 深度链接功能，使用 iOS 13+ 推荐的 SceneDelegate 方式。

## 第一步：验证当前配置

### 检查 AASA 文件

```bash
curl -v https://picadabra-ai.onelink.me/.well-known/apple-app-site-association
```

应该返回包含你的 App ID 的 JSON 配置。

### 检查 Entitlements

确认 `ChatToDesign.entitlements` 包含：

```xml
<key>com.apple.developer.associated-domains</key>
<array>
    <string>applinks:picadabra-ai.onelink.me</string>
</array>
```

### 检查 URL Scheme

确认 `Info.plist` 包含自定义 URL Scheme：

```xml
<key>CFBundleURLTypes</key>
<array>
    <!-- 现有的 Google 登录 URL Scheme -->
    <dict>...</dict>

    <!-- 自定义 URL Scheme -->
    <dict>
        <key>CFBundleTypeRole</key>
        <string>Editor</string>
        <key>CFBundleURLName</key>
        <string>com.a1d.chat-to-design</string>
        <key>CFBundleURLSchemes</key>
        <array>
            <string>chattodesign</string>
        </array>
    </dict>
</array>
```

## 第二步：创建 SceneDelegate

创建一个专门的 SceneDelegate 类来处理 Universal Links：

```swift
// 新建文件 ChatToDesign/Application/SceneDelegate.swift
import UIKit
import SwiftUI
import AppsFlyerLib
import GoogleSignIn

class SceneDelegate: NSObject, UIWindowSceneDelegate {

    var window: UIWindow?

    // MARK: - Universal Links 处理

    func scene(_ scene: UIScene, continue userActivity: NSUserActivity) {
        Logger.info("SceneDelegate: Received Universal Link")

        guard userActivity.activityType == NSUserActivityTypeBrowsingWeb,
              let url = userActivity.webpageURL else {
            Logger.warning("SceneDelegate: Invalid user activity for Universal Link")
            return
        }

        Logger.info("SceneDelegate: Processing Universal Link - \(url.absoluteString)")

        // 1. 通知 AppsFlyer SDK
        AppsFlyerLib.shared().continue(userActivity, restorationHandler: nil)

        // 2. 处理深度链接路由
        let container = AppDependencyContainer.shared
        container.deepLinkHandler.processDeepLink(from: url)
    }

    // MARK: - URL Schemes 处理

    func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
        guard let urlContext = URLContexts.first else { return }

        let url = urlContext.url
        Logger.info("SceneDelegate: Processing URL Scheme - \(url.absoluteString)")

        // 1. 检查是否是 Google Sign-In URL
        if GIDSignIn.sharedInstance.handle(url) {
            return
        }

        // 2. 处理 AppsFlyer 深度链接
        let container = AppDependencyContainer.shared
        container.deepLinkHandler.processDeepLink(from: url)
    }
}
```

## 第三步：更新 ChatToDesignApp.swift

修改 SwiftUI App 文件以使用 SceneDelegate：

```swift
// 修改 ChatToDesignApp.swift
@main
struct ChatToDesignApp: App {
    // 注册 AppDelegate
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate

    // 注册 SceneDelegate
    @UIApplicationDelegateAdaptor(SceneDelegate.self) var sceneDelegate

    // 环境变量
    @Environment(\.scenePhase) private var scenePhase

    var body: some Scene {
        WindowGroup {
            ZStack {
                RootView()
            }
            .preferredColorScheme(.dark)
        }
        .onChange(of: scenePhase) { newPhase in
            // 可以在这里处理场景状态变化
            switch newPhase {
            case .active:
                Logger.info("App: Scene became active")
            case .inactive:
                Logger.info("App: Scene became inactive")
            case .background:
                Logger.info("App: Scene moved to background")
            @unknown default:
                break
            }
        }
    }
}
```

## 第四步：更新 Info.plist

添加 Scene 配置到 Info.plist：

```xml
<key>UIApplicationSceneManifest</key>
<dict>
    <key>UIApplicationSupportsMultipleScenes</key>
    <false/>
    <key>UISceneConfigurations</key>
    <dict>
        <key>UIWindowSceneSessionRoleApplication</key>
        <array>
            <dict>
                <key>UISceneConfigurationName</key>
                <string>Default Configuration</string>
                <key>UISceneDelegateClassName</key>
                <string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
            </dict>
        </array>
    </dict>
</dict>
```

## 第五步：更新 AppsFlyerAnalyticsAdapter

在 `AppsFlyerAnalyticsAdapter.swift` 中实现深度链接代理：

```swift
// 在 configureAppsFlyerSDK 方法中确保设置代理
private func configureAppsFlyerSDK(with config: AppsFlyerConfiguration) async {
    // ... 现有配置代码 ...

    // 确保设置深度链接代理
    if config.deepLinkingEnabled {
        AppsFlyerLib.shared().deepLinkDelegate = self
    }
}

// 在文件末尾添加扩展
extension AppsFlyerAnalyticsAdapter: AppsFlyerDeepLinkDelegate {
    public func didResolveDeepLink(_ result: AppsFlyerDeepLinkResult) {
        logger.info("AppsFlyer UDL: Deep link resolved with status: \(result.status)")

        // 委托给专门的深度链接处理器
        let container = AppDependencyContainer.shared
        container.deepLinkHandler.processDeepLink(result)
    }
}
```

## 第六步：增强 AppsFlyerDeepLinkHandler

在 `AppsFlyerDeepLinkHandler.swift` 中添加 UDL 支持：

```swift
// 添加这个方法到 AppsFlyerDeepLinkHandler 类中
public func processDeepLink(_ result: AppsFlyerDeepLinkResult) {
    isProcessingDeepLink = true

    switch result.status {
    case .found:
        handleUDLDeepLinkFound(result)
    case .notFound:
        handleDeepLinkNotFound()
    case .failure:
        if let error = result.error {
            handleDeepLinkFailure(error)
        }
    @unknown default:
        handleUnknownDeepLinkStatus()
    }

    isProcessingDeepLink = false
}

private func handleUDLDeepLinkFound(_ result: AppsFlyerDeepLinkResult) {
    guard let deepLink = result.deepLink else {
        logger.warning("UDL: Deep link found but no data available")
        return
    }

    logger.info("UDL: Processing deep link - isDeferred: \(deepLink.isDeferred)")

    // 提取参数
    let clickEvent = deepLink.clickEvent
    deepLinkData = clickEvent

    // 创建参数对象
    let parameters = extractDeepLinkParameters(from: clickEvent)

    // 如果有 deeplinkValue，优先使用
    if let deeplinkValue = deepLink.deeplinkValue {
        parameters.deepLinkValue = deeplinkValue
    }

    // 路由到目标
    routeToDestination(parameters: parameters)

    // 追踪事件
    trackDeepLinkEvent(parameters: parameters.allParameters, status: "udl_found")
}
```

## 第七步：更新路由通知

在 `AppsFlyerDeepLinkHandler.swift` 中添加新的通知：

```swift
// 在文件末尾添加新的通知名称
extension Notification.Name {
    static let deepLinkRouteToCreate = Notification.Name("deepLinkRouteToCreate")
    static let deepLinkRouteToExplore = Notification.Name("deepLinkRouteToExplore")
    static let deepLinkRouteToTemplate = Notification.Name("deepLinkRouteToTemplate")
}
```

并在 `routeToDestination` 方法中添加新的路由逻辑：

```swift
// 在现有的 switch 语句中添加新的 case
case "create":
    routeToCreate(parameters: parameters)
case "explore":
    routeToExplore(parameters: parameters)
case "template":
    routeToTemplate(parameters: parameters)

// 添加对应的路由方法
private func routeToCreate(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkHandler: Routing to create")

    NotificationCenter.default.post(
        name: .deepLinkRouteToCreate,
        object: nil,
        userInfo: ["parameters": parameters]
    )
}

private func routeToExplore(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkHandler: Routing to explore")

    NotificationCenter.default.post(
        name: .deepLinkRouteToExplore,
        object: nil,
        userInfo: ["parameters": parameters]
    )
}

private func routeToTemplate(parameters: DeepLinkParameters) {
    logger.info("AppsFlyerDeepLinkHandler: Routing to template")

    NotificationCenter.default.post(
        name: .deepLinkRouteToTemplate,
        object: nil,
        userInfo: ["parameters": parameters]
    )
}
```

## 第八步：更新 MainTabView

在 `MainTabView.swift` 中添加深度链接监听：

```swift
// 在 body 的 ZStack 中添加这些监听器
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToCreate)) { _ in
    selectedTab = .create
}
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToExplore)) { _ in
    selectedTab = .explore
}
.onReceive(NotificationCenter.default.publisher(for: .deepLinkRouteToSubscription)) { _ in
    // 显示订阅页面
    NotificationCenter.default.post(name: .showSubscriptionPage, object: nil)
}
```

## 第九步：测试

### 基础测试链接

```
https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=create
https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=explore
https://picadabra-ai.onelink.me/H5hv?pid=test&deep_link_value=subscription
```

### URL Scheme 测试链接

```
chattodesign://create
chattodesign://explore?param1=value1&param2=value2
```

### 测试步骤

#### Universal Links 测试

1. 在 Safari 中打开测试链接
2. 确认应用正确打开并路由到对应页面
3. 检查控制台日志确认深度链接被正确处理

#### URL Scheme 测试

1. 在 Safari 中输入 URL Scheme 链接
2. 确认应用正确打开并路由到对应页面
3. 检查 SceneDelegate 中的日志输出

#### 延迟深度链接测试

1. 卸载应用
2. 在 Safari 中打开测试链接
3. 安装应用
4. 首次启动时确认是否路由到正确页面

## 常见问题

### Q: Universal Links 不工作？

A: 检查 AASA 文件是否可访问，确认 Entitlements 配置正确，确认 SceneDelegate 正确注册

### Q: 深度链接参数丢失？

A: 确认 `didResolveDeepLink` 方法被正确调用，检查参数提取逻辑

### Q: 路由不生效？

A: 确认通知监听器已正确设置，检查通知名称是否匹配

### Q: SceneDelegate 没有被调用？

A: 确认 Info.plist 中的 UIApplicationSceneManifest 配置正确，确认 ChatToDesignApp.swift 中正确注册了 SceneDelegate

## 下一步

完成基础集成后，你可以：

1. 在 AppsFlyer 后台创建 OneLink 模板
2. 配置具体的营销活动链接
3. 添加更多自定义参数和路由逻辑
4. 集成 A/B 测试和个性化功能

## 相关文档

- [完整技术方案](./appsflyer-onelink-integration-plan.md)
- [AppsFlyer iOS UDL 官方文档](https://dev.appsflyer.com/hc/docs/dl_ios_unified_deep_linking)
- [Apple Universal Links 文档](https://developer.apple.com/documentation/xcode/supporting-universal-links-in-your-app)
- [Apple Scene-Based Lifecycle 文档](https://developer.apple.com/documentation/uikit/app_and_environment/scenes)
